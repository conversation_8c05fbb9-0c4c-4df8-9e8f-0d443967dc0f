{{--@extends('auth.layouts.authentication')--}}
@extends('frontend.suruchi.layouts.app')

@section('content')

    <div class="login__section section--padding">
        <div class="container">
            <form class="form-default account__login--inner" role="form" action="{{ route('login') }}" method="POST">
                @csrf
                <div class="login__section--inner">
                    <div class="row">
                        <div class="col-md-3 col-lg-3 col-sm-0"></div>
                        <div class="col-md-6 col-lg-6">
                            <div class="account__login">
                                <div class="account__login--header mb-25">
                                    <h2 class="account__login--header__title h3 mb-10">{{ translate('Welcome Back!')}}</h2>
                                    <p class="account__login--header__desc">Login if you are a returning customer.</p>
                                </div>
                                <div class="account__login--inner">
                                    <!-- Conditional Email/Phone Login -->
                                    @if (get_setting('otp_system'))
                                        <div class="form-group phone-form-group mb-1">
                                            <input type="tel" id="phone-code" class="account__login--input" name="phone" placeholder="{{  translate('Phone') }}" value="{{ old('phone') }}" autocomplete="off">
                                        </div>
                                        <input type="hidden" name="country_code" value="">
                                        <div class="form-group email-form-group mb-1 d-none">
                                            <input type="email" class="account__login--input" name="email" placeholder="{{ translate('Email') }}" value="{{ old('email') }}" autocomplete="off">
                                            @error('email')
                                            <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                                            @enderror
                                        </div>
                                        <button type="button" class="btn btn-link p-0 text-primary fs-12" onclick="toggleEmailPhone(this)">
                                            <i>*{{ translate('Use Email Instead') }}</i>
                                        </button>
                                    @else
                                        <div class="form-group">
                                            <input type="email" class="account__login--input" placeholder="{{  translate('Email') }}" name="email" value="{{ old('email') }}" autocomplete="off">
                                            @error('email')
                                            <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                                            @enderror
                                        </div>
                                    @endif

                                    <!-- OTP Condition -->
                                    @if (get_setting('ask_email_otp_to_login'))
                                        <div id="otp-input-group" class="form-group d-none">
                                            <input type="text" id="email_otp" name="email_otp" class="account__login--input" placeholder="{{ translate('Enter OTP') }}" autocomplete="off">
                                        </div>
                                        <button type="button" class="btn btn-primary btn-block" onclick="loginWithEmailOtp()">{{ translate('Send OTP') }}</button>
                                    @else
                                        <!-- Password Field -->
                                        <div class="form-group">
                                            <label for="password" class="fs-12 fw-700 text-soft-dark"></label>
                                            <div class="position-relative">
                                                <input type="password" class="account__login--input" name="password" placeholder="{{ translate('Password')}}">
                                                <i class="password-toggle las la-eye"></i>
                                            </div>
                                        </div>
                                    @endif

                                    <div class="account__login--remember__forgot mb-15 d-flex justify-content-between align-items-center">
                                        <div class="account__login--remember position__relative">
                                            <input class="checkout__checkbox--input" id="check1" type="checkbox" {{ old('remember') ? 'checked' : '' }}>
                                            <span class="checkout__checkbox--checkmark"></span>
                                            <label class="checkout__checkbox--label login__remember--label" for="check1">
                                                Remember me</label>
                                        </div>
                                        <a href="{{ route('password.request') }}" class="account__login--forgot">Forgot Your Password?</a>
                                    </div>

                                    <!-- Login Button -->
                                    <button class="account__login--btn primary__btn" type="submit">Login</button>

                                    <!-- Social Login -->
                                    @if(get_setting('google_login') || get_setting('facebook_login') || get_setting('twitter_login') || get_setting('apple_login'))
                                        <div class="account__login--divide"><span class="account__login--divide__text">OR</span></div>
                                        <div class="account__social d-flex justify-content-center mb-15">
                                            @if(get_setting('facebook_login'))
                                                <a href="{{ route('social.login', ['provider' => 'facebook']) }}" class="account__social--link facebook">Facebook</a>
                                            @endif
                                            @if(get_setting('google_login'))
                                                <a href="{{ route('social.login', ['provider' => 'google']) }}" class="account__social--link google">Google</a>
                                            @endif
                                            @if(get_setting('twitter_login'))
                                                <a href="{{ route('social.login', ['provider' => 'twitter']) }}" class="account__social--link twitter">Twitter</a>
                                            @endif
                                            @if(get_setting('apple_login'))
                                                <a href="{{ route('social.login', ['provider' => 'apple']) }}" class="account__social--link apple">Apple</a>
                                            @endif
                                        </div>
                                    @endif

                                    <!-- Register Now and Back Links -->
                                    <p class="account__login--signup__text">{{ translate("Don't have an account?") }}
                                        <a href="{{ route('user.registration') }}">Register now</a>
                                    </p>
                                    <a href="{{ url()->previous() }}" class="mt-3 fs-14 fw-700 text-primary">
                                        <i class="las la-arrow-left fs-20 mr-1"></i>{{ translate('Back to Previous Page') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('script')
    <script type="text/javascript">
        function autoFillCustomer(){
            $('#email').val('<EMAIL>');
            $('#password').val('123456');
        }

        function loginWithEmailOtp() {
            const email = document.getElementById('email').value;
            const emailOtp = document.getElementById('email_otp').value;

            // If OTP field is not empty, submit the form
            if (emailOtp.trim() !== '') {
                const form = document.querySelector('.account__login--inner');
                // Set the action to the cart.login.submit route
                form.action = "{{ route('cart.login.submit') }}"; // Set form action to the desired route
                form.submit();
            } else {
                // Otherwise, send the OTP
                $.ajax({
                    url: "{{ route('user.login.send-otp') }}",
                    type: "POST",
                    data: {
                        _token: "{{ csrf_token() }}",
                        email: email
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            $('#email-input-group').hide();
                            $('#otp-input-group').removeClass('d-none');
                            $('#login-button').text("{{ translate('Login') }}"); // Change button text
                        } else {
                            alert(response.message);
                        }
                    },
                    error: function() {
                        alert("Error sending OTP. Please try again.");
                    }
                });
            }
        }
    </script>
@endsection
